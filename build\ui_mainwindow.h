/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.15.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QWidget>
#include <qcustomplot.h>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralWidget;
    QGridLayout *gridLayout_7;
    QGridLayout *gridLayout_6;
    QGridLayout *gridLayout_5;
    QGroupBox *groupBox;
    QGridLayout *gridLayout;
    QLabel *label;
    QComboBox *portName;
    QLabel *label_2;
    QComboBox *buadRate;
    QLabel *label_3;
    QComboBox *dataBits;
    QLabel *label_4;
    QComboBox *parity;
    QLabel *label_5;
    QComboBox *stopBits;
    QLabel *label_6;
    QComboBox *flowControl;
    QGridLayout *gridLayout_2;
    QLabel *label_8;
    QLineEdit *sAddr1;
    QLabel *label_startReg;
    QLineEdit *startRegister;
    QPushButton *openButton;
    QPushButton *saveButton;
    QPushButton *mReadBtn;
    QGroupBox *groupBox_3;
    QGridLayout *gridLayout_4;
    QLineEdit *le_totalTime;
    QPushButton *btn_resettime;
    QHBoxLayout *horizontalLayout;
    QCheckBox *checkBox_2;
    QLineEdit *timelineEdit_2;
    QLabel *label_10;
    QLabel *label_7;
    QGridLayout *gridLayout_3;
    QLabel *label_9;
    QHBoxLayout *horizontalLayout_2;
    QCheckBox *cb_tmp1;
    QLineEdit *nowwd;
    QLabel *label_11;
    QHBoxLayout *horizontalLayout_4;
    QCheckBox *cb_tmp2;
    QLineEdit *nowwd_2;
    QLabel *label_12;
    QHBoxLayout *horizontalLayout_7;
    QCheckBox *cb_tmp3;
    QLineEdit *nowwd_3;
    QLabel *label_13;
    QHBoxLayout *horizontalLayout_8;
    QCheckBox *cb_tmp4;
    QLineEdit *nowwd_4;
    QLabel *label_14;
    QHBoxLayout *horizontalLayout_9;
    QCheckBox *cb_tmp5;
    QLineEdit *nowwd_5;
    QLabel *label_15;
    QHBoxLayout *horizontalLayout_10;
    QCheckBox *cb_tmp6;
    QLineEdit *nowwd_6;
    QLabel *label_16;
    QHBoxLayout *horizontalLayout_11;
    QCheckBox *cb_tmp7;
    QLineEdit *nowwd_7;
    QLabel *label_17;
    QHBoxLayout *horizontalLayout_3;
    QCheckBox *cb_tmp8;
    QLineEdit *nowwd_8;
    QLabel *label_18;
    QHBoxLayout *horizontalLayout_5;
    QCheckBox *cb_tmp9;
    QLineEdit *nowwd_9;
    QLabel *label_19;
    QHBoxLayout *horizontalLayout_6;
    QCheckBox *cb_tmp10;
    QLineEdit *nowwd_10;
    QGroupBox *groupBox_2;
    QGridLayout *gridLayout_8;
    QCustomPlot *PlotCurve;
    QMenuBar *menuBar;
    QToolBar *mainToolBar;
    QStatusBar *statusBar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QString::fromUtf8("MainWindow"));
        MainWindow->resize(1579, 766);
        centralWidget = new QWidget(MainWindow);
        centralWidget->setObjectName(QString::fromUtf8("centralWidget"));
        gridLayout_7 = new QGridLayout(centralWidget);
        gridLayout_7->setSpacing(6);
        gridLayout_7->setContentsMargins(11, 11, 11, 11);
        gridLayout_7->setObjectName(QString::fromUtf8("gridLayout_7"));
        gridLayout_6 = new QGridLayout();
        gridLayout_6->setSpacing(6);
        gridLayout_6->setObjectName(QString::fromUtf8("gridLayout_6"));
        gridLayout_5 = new QGridLayout();
        gridLayout_5->setSpacing(6);
        gridLayout_5->setObjectName(QString::fromUtf8("gridLayout_5"));
        groupBox = new QGroupBox(centralWidget);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        gridLayout = new QGridLayout(groupBox);
        gridLayout->setSpacing(6);
        gridLayout->setContentsMargins(11, 11, 11, 11);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        label = new QLabel(groupBox);
        label->setObjectName(QString::fromUtf8("label"));

        gridLayout->addWidget(label, 0, 0, 1, 1);

        portName = new QComboBox(groupBox);
        portName->setObjectName(QString::fromUtf8("portName"));

        gridLayout->addWidget(portName, 0, 1, 1, 1);

        label_2 = new QLabel(groupBox);
        label_2->setObjectName(QString::fromUtf8("label_2"));

        gridLayout->addWidget(label_2, 1, 0, 1, 1);

        buadRate = new QComboBox(groupBox);
        buadRate->setObjectName(QString::fromUtf8("buadRate"));

        gridLayout->addWidget(buadRate, 1, 1, 1, 1);

        label_3 = new QLabel(groupBox);
        label_3->setObjectName(QString::fromUtf8("label_3"));

        gridLayout->addWidget(label_3, 2, 0, 1, 1);

        dataBits = new QComboBox(groupBox);
        dataBits->setObjectName(QString::fromUtf8("dataBits"));

        gridLayout->addWidget(dataBits, 2, 1, 1, 1);

        label_4 = new QLabel(groupBox);
        label_4->setObjectName(QString::fromUtf8("label_4"));

        gridLayout->addWidget(label_4, 3, 0, 1, 1);

        parity = new QComboBox(groupBox);
        parity->setObjectName(QString::fromUtf8("parity"));

        gridLayout->addWidget(parity, 3, 1, 1, 1);

        label_5 = new QLabel(groupBox);
        label_5->setObjectName(QString::fromUtf8("label_5"));

        gridLayout->addWidget(label_5, 4, 0, 1, 1);

        stopBits = new QComboBox(groupBox);
        stopBits->setObjectName(QString::fromUtf8("stopBits"));

        gridLayout->addWidget(stopBits, 4, 1, 1, 1);

        label_6 = new QLabel(groupBox);
        label_6->setObjectName(QString::fromUtf8("label_6"));

        gridLayout->addWidget(label_6, 5, 0, 1, 1);

        flowControl = new QComboBox(groupBox);
        flowControl->setObjectName(QString::fromUtf8("flowControl"));

        gridLayout->addWidget(flowControl, 5, 1, 1, 1);

        gridLayout->setColumnStretch(0, 1);
        gridLayout->setColumnStretch(1, 2);

        gridLayout_5->addWidget(groupBox, 0, 0, 1, 1);

        gridLayout_2 = new QGridLayout();
        gridLayout_2->setSpacing(6);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        label_8 = new QLabel(centralWidget);
        label_8->setObjectName(QString::fromUtf8("label_8"));

        gridLayout_2->addWidget(label_8, 0, 0, 1, 1);

        sAddr1 = new QLineEdit(centralWidget);
        sAddr1->setObjectName(QString::fromUtf8("sAddr1"));
        QSizePolicy sizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(sAddr1->sizePolicy().hasHeightForWidth());
        sAddr1->setSizePolicy(sizePolicy);

        gridLayout_2->addWidget(sAddr1, 0, 1, 1, 1);

        label_startReg = new QLabel(centralWidget);
        label_startReg->setObjectName(QString::fromUtf8("label_startReg"));

        gridLayout_2->addWidget(label_startReg, 1, 0, 1, 1);

        startRegister = new QLineEdit(centralWidget);
        startRegister->setObjectName(QString::fromUtf8("startRegister"));
        sizePolicy.setHeightForWidth(startRegister->sizePolicy().hasHeightForWidth());
        startRegister->setSizePolicy(sizePolicy);

        gridLayout_2->addWidget(startRegister, 1, 1, 1, 1);

        openButton = new QPushButton(centralWidget);
        openButton->setObjectName(QString::fromUtf8("openButton"));
        QSizePolicy sizePolicy1(QSizePolicy::Minimum, QSizePolicy::Fixed);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(openButton->sizePolicy().hasHeightForWidth());
        openButton->setSizePolicy(sizePolicy1);

        gridLayout_2->addWidget(openButton, 2, 0, 1, 2);

        saveButton = new QPushButton(centralWidget);
        saveButton->setObjectName(QString::fromUtf8("saveButton"));
        sizePolicy1.setHeightForWidth(saveButton->sizePolicy().hasHeightForWidth());
        saveButton->setSizePolicy(sizePolicy1);

        gridLayout_2->addWidget(saveButton, 3, 0, 1, 2);

        mReadBtn = new QPushButton(centralWidget);
        mReadBtn->setObjectName(QString::fromUtf8("mReadBtn"));
        sizePolicy1.setHeightForWidth(mReadBtn->sizePolicy().hasHeightForWidth());
        mReadBtn->setSizePolicy(sizePolicy1);

        gridLayout_2->addWidget(mReadBtn, 4, 0, 1, 2);


        gridLayout_5->addLayout(gridLayout_2, 1, 0, 1, 1);

        groupBox_3 = new QGroupBox(centralWidget);
        groupBox_3->setObjectName(QString::fromUtf8("groupBox_3"));
        gridLayout_4 = new QGridLayout(groupBox_3);
        gridLayout_4->setSpacing(6);
        gridLayout_4->setContentsMargins(11, 11, 11, 11);
        gridLayout_4->setObjectName(QString::fromUtf8("gridLayout_4"));
        le_totalTime = new QLineEdit(groupBox_3);
        le_totalTime->setObjectName(QString::fromUtf8("le_totalTime"));
        QSizePolicy sizePolicy2(QSizePolicy::Preferred, QSizePolicy::Preferred);
        sizePolicy2.setHorizontalStretch(0);
        sizePolicy2.setVerticalStretch(0);
        sizePolicy2.setHeightForWidth(le_totalTime->sizePolicy().hasHeightForWidth());
        le_totalTime->setSizePolicy(sizePolicy2);
        QFont font;
        font.setPointSize(16);
        font.setBold(false);
        le_totalTime->setFont(font);
#if QT_CONFIG(tooltip)
        le_totalTime->setToolTip(0u);
#endif // QT_CONFIG(tooltip)
        le_totalTime->setAlignment(Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter);

        gridLayout_4->addWidget(le_totalTime, 3, 0, 1, 1);

        btn_resettime = new QPushButton(groupBox_3);
        btn_resettime->setObjectName(QString::fromUtf8("btn_resettime"));
        sizePolicy1.setHeightForWidth(btn_resettime->sizePolicy().hasHeightForWidth());
        btn_resettime->setSizePolicy(sizePolicy1);

        gridLayout_4->addWidget(btn_resettime, 4, 0, 1, 1);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setSpacing(6);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        checkBox_2 = new QCheckBox(groupBox_3);
        checkBox_2->setObjectName(QString::fromUtf8("checkBox_2"));

        horizontalLayout->addWidget(checkBox_2);

        timelineEdit_2 = new QLineEdit(groupBox_3);
        timelineEdit_2->setObjectName(QString::fromUtf8("timelineEdit_2"));
        sizePolicy.setHeightForWidth(timelineEdit_2->sizePolicy().hasHeightForWidth());
        timelineEdit_2->setSizePolicy(sizePolicy);

        horizontalLayout->addWidget(timelineEdit_2);

        label_10 = new QLabel(groupBox_3);
        label_10->setObjectName(QString::fromUtf8("label_10"));

        horizontalLayout->addWidget(label_10);


        gridLayout_4->addLayout(horizontalLayout, 0, 0, 1, 1);

        label_7 = new QLabel(groupBox_3);
        label_7->setObjectName(QString::fromUtf8("label_7"));
        QSizePolicy sizePolicy3(QSizePolicy::Preferred, QSizePolicy::Fixed);
        sizePolicy3.setHorizontalStretch(0);
        sizePolicy3.setVerticalStretch(0);
        sizePolicy3.setHeightForWidth(label_7->sizePolicy().hasHeightForWidth());
        label_7->setSizePolicy(sizePolicy3);
        QFont font1;
        font1.setPointSize(10);
        font1.setBold(true);
        label_7->setFont(font1);

        gridLayout_4->addWidget(label_7, 1, 0, 1, 1);


        gridLayout_5->addWidget(groupBox_3, 2, 0, 1, 1);


        gridLayout_6->addLayout(gridLayout_5, 0, 0, 1, 1);

        gridLayout_3 = new QGridLayout();
        gridLayout_3->setSpacing(6);
        gridLayout_3->setObjectName(QString::fromUtf8("gridLayout_3"));
        label_9 = new QLabel(centralWidget);
        label_9->setObjectName(QString::fromUtf8("label_9"));

        gridLayout_3->addWidget(label_9, 0, 0, 1, 1);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setSpacing(6);
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        cb_tmp1 = new QCheckBox(centralWidget);
        cb_tmp1->setObjectName(QString::fromUtf8("cb_tmp1"));

        horizontalLayout_2->addWidget(cb_tmp1);

        nowwd = new QLineEdit(centralWidget);
        nowwd->setObjectName(QString::fromUtf8("nowwd"));
        QSizePolicy sizePolicy4(QSizePolicy::Expanding, QSizePolicy::Expanding);
        sizePolicy4.setHorizontalStretch(0);
        sizePolicy4.setVerticalStretch(0);
        sizePolicy4.setHeightForWidth(nowwd->sizePolicy().hasHeightForWidth());
        nowwd->setSizePolicy(sizePolicy4);
        QFont font2;
        font2.setPointSize(10);
        nowwd->setFont(font2);

        horizontalLayout_2->addWidget(nowwd);


        gridLayout_3->addLayout(horizontalLayout_2, 1, 0, 1, 1);

        label_11 = new QLabel(centralWidget);
        label_11->setObjectName(QString::fromUtf8("label_11"));

        gridLayout_3->addWidget(label_11, 2, 0, 1, 1);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setSpacing(6);
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        cb_tmp2 = new QCheckBox(centralWidget);
        cb_tmp2->setObjectName(QString::fromUtf8("cb_tmp2"));

        horizontalLayout_4->addWidget(cb_tmp2);

        nowwd_2 = new QLineEdit(centralWidget);
        nowwd_2->setObjectName(QString::fromUtf8("nowwd_2"));
        sizePolicy4.setHeightForWidth(nowwd_2->sizePolicy().hasHeightForWidth());
        nowwd_2->setSizePolicy(sizePolicy4);
        nowwd_2->setFont(font2);

        horizontalLayout_4->addWidget(nowwd_2);


        gridLayout_3->addLayout(horizontalLayout_4, 3, 0, 1, 1);

        label_12 = new QLabel(centralWidget);
        label_12->setObjectName(QString::fromUtf8("label_12"));

        gridLayout_3->addWidget(label_12, 4, 0, 1, 1);

        horizontalLayout_7 = new QHBoxLayout();
        horizontalLayout_7->setSpacing(6);
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        cb_tmp3 = new QCheckBox(centralWidget);
        cb_tmp3->setObjectName(QString::fromUtf8("cb_tmp3"));

        horizontalLayout_7->addWidget(cb_tmp3);

        nowwd_3 = new QLineEdit(centralWidget);
        nowwd_3->setObjectName(QString::fromUtf8("nowwd_3"));
        sizePolicy4.setHeightForWidth(nowwd_3->sizePolicy().hasHeightForWidth());
        nowwd_3->setSizePolicy(sizePolicy4);
        nowwd_3->setFont(font2);

        horizontalLayout_7->addWidget(nowwd_3);


        gridLayout_3->addLayout(horizontalLayout_7, 5, 0, 1, 1);

        label_13 = new QLabel(centralWidget);
        label_13->setObjectName(QString::fromUtf8("label_13"));

        gridLayout_3->addWidget(label_13, 6, 0, 1, 1);

        horizontalLayout_8 = new QHBoxLayout();
        horizontalLayout_8->setSpacing(6);
        horizontalLayout_8->setObjectName(QString::fromUtf8("horizontalLayout_8"));
        cb_tmp4 = new QCheckBox(centralWidget);
        cb_tmp4->setObjectName(QString::fromUtf8("cb_tmp4"));

        horizontalLayout_8->addWidget(cb_tmp4);

        nowwd_4 = new QLineEdit(centralWidget);
        nowwd_4->setObjectName(QString::fromUtf8("nowwd_4"));
        sizePolicy4.setHeightForWidth(nowwd_4->sizePolicy().hasHeightForWidth());
        nowwd_4->setSizePolicy(sizePolicy4);
        nowwd_4->setFont(font2);

        horizontalLayout_8->addWidget(nowwd_4);


        gridLayout_3->addLayout(horizontalLayout_8, 7, 0, 1, 1);

        label_14 = new QLabel(centralWidget);
        label_14->setObjectName(QString::fromUtf8("label_14"));

        gridLayout_3->addWidget(label_14, 8, 0, 1, 1);

        horizontalLayout_9 = new QHBoxLayout();
        horizontalLayout_9->setSpacing(6);
        horizontalLayout_9->setObjectName(QString::fromUtf8("horizontalLayout_9"));
        cb_tmp5 = new QCheckBox(centralWidget);
        cb_tmp5->setObjectName(QString::fromUtf8("cb_tmp5"));

        horizontalLayout_9->addWidget(cb_tmp5);

        nowwd_5 = new QLineEdit(centralWidget);
        nowwd_5->setObjectName(QString::fromUtf8("nowwd_5"));
        sizePolicy4.setHeightForWidth(nowwd_5->sizePolicy().hasHeightForWidth());
        nowwd_5->setSizePolicy(sizePolicy4);
        nowwd_5->setFont(font2);

        horizontalLayout_9->addWidget(nowwd_5);


        gridLayout_3->addLayout(horizontalLayout_9, 9, 0, 1, 1);

        label_15 = new QLabel(centralWidget);
        label_15->setObjectName(QString::fromUtf8("label_15"));

        gridLayout_3->addWidget(label_15, 10, 0, 1, 1);

        horizontalLayout_10 = new QHBoxLayout();
        horizontalLayout_10->setSpacing(6);
        horizontalLayout_10->setObjectName(QString::fromUtf8("horizontalLayout_10"));
        cb_tmp6 = new QCheckBox(centralWidget);
        cb_tmp6->setObjectName(QString::fromUtf8("cb_tmp6"));

        horizontalLayout_10->addWidget(cb_tmp6);

        nowwd_6 = new QLineEdit(centralWidget);
        nowwd_6->setObjectName(QString::fromUtf8("nowwd_6"));
        sizePolicy4.setHeightForWidth(nowwd_6->sizePolicy().hasHeightForWidth());
        nowwd_6->setSizePolicy(sizePolicy4);
        nowwd_6->setFont(font2);

        horizontalLayout_10->addWidget(nowwd_6);


        gridLayout_3->addLayout(horizontalLayout_10, 11, 0, 1, 1);

        label_16 = new QLabel(centralWidget);
        label_16->setObjectName(QString::fromUtf8("label_16"));

        gridLayout_3->addWidget(label_16, 12, 0, 1, 1);

        horizontalLayout_11 = new QHBoxLayout();
        horizontalLayout_11->setSpacing(6);
        horizontalLayout_11->setObjectName(QString::fromUtf8("horizontalLayout_11"));
        cb_tmp7 = new QCheckBox(centralWidget);
        cb_tmp7->setObjectName(QString::fromUtf8("cb_tmp7"));

        horizontalLayout_11->addWidget(cb_tmp7);

        nowwd_7 = new QLineEdit(centralWidget);
        nowwd_7->setObjectName(QString::fromUtf8("nowwd_7"));
        sizePolicy4.setHeightForWidth(nowwd_7->sizePolicy().hasHeightForWidth());
        nowwd_7->setSizePolicy(sizePolicy4);
        nowwd_7->setFont(font2);

        horizontalLayout_11->addWidget(nowwd_7);


        gridLayout_3->addLayout(horizontalLayout_11, 13, 0, 1, 1);

        label_17 = new QLabel(centralWidget);
        label_17->setObjectName(QString::fromUtf8("label_17"));

        gridLayout_3->addWidget(label_17, 14, 0, 1, 1);

        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setSpacing(6);
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        cb_tmp8 = new QCheckBox(centralWidget);
        cb_tmp8->setObjectName(QString::fromUtf8("cb_tmp8"));

        horizontalLayout_3->addWidget(cb_tmp8);

        nowwd_8 = new QLineEdit(centralWidget);
        nowwd_8->setObjectName(QString::fromUtf8("nowwd_8"));
        sizePolicy4.setHeightForWidth(nowwd_8->sizePolicy().hasHeightForWidth());
        nowwd_8->setSizePolicy(sizePolicy4);
        nowwd_8->setFont(font2);

        horizontalLayout_3->addWidget(nowwd_8);


        gridLayout_3->addLayout(horizontalLayout_3, 15, 0, 1, 1);

        label_18 = new QLabel(centralWidget);
        label_18->setObjectName(QString::fromUtf8("label_18"));

        gridLayout_3->addWidget(label_18, 16, 0, 1, 1);

        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setSpacing(6);
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        cb_tmp9 = new QCheckBox(centralWidget);
        cb_tmp9->setObjectName(QString::fromUtf8("cb_tmp9"));

        horizontalLayout_5->addWidget(cb_tmp9);

        nowwd_9 = new QLineEdit(centralWidget);
        nowwd_9->setObjectName(QString::fromUtf8("nowwd_9"));
        sizePolicy4.setHeightForWidth(nowwd_9->sizePolicy().hasHeightForWidth());
        nowwd_9->setSizePolicy(sizePolicy4);
        nowwd_9->setFont(font2);

        horizontalLayout_5->addWidget(nowwd_9);


        gridLayout_3->addLayout(horizontalLayout_5, 17, 0, 1, 1);

        label_19 = new QLabel(centralWidget);
        label_19->setObjectName(QString::fromUtf8("label_19"));

        gridLayout_3->addWidget(label_19, 18, 0, 1, 1);

        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setSpacing(6);
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        cb_tmp10 = new QCheckBox(centralWidget);
        cb_tmp10->setObjectName(QString::fromUtf8("cb_tmp10"));

        horizontalLayout_6->addWidget(cb_tmp10);

        nowwd_10 = new QLineEdit(centralWidget);
        nowwd_10->setObjectName(QString::fromUtf8("nowwd_10"));
        sizePolicy4.setHeightForWidth(nowwd_10->sizePolicy().hasHeightForWidth());
        nowwd_10->setSizePolicy(sizePolicy4);
        nowwd_10->setFont(font2);

        horizontalLayout_6->addWidget(nowwd_10);


        gridLayout_3->addLayout(horizontalLayout_6, 19, 0, 1, 1);


        gridLayout_6->addLayout(gridLayout_3, 0, 1, 1, 1);

        groupBox_2 = new QGroupBox(centralWidget);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        gridLayout_8 = new QGridLayout(groupBox_2);
        gridLayout_8->setSpacing(6);
        gridLayout_8->setContentsMargins(11, 11, 11, 11);
        gridLayout_8->setObjectName(QString::fromUtf8("gridLayout_8"));
        PlotCurve = new QCustomPlot(groupBox_2);
        PlotCurve->setObjectName(QString::fromUtf8("PlotCurve"));

        gridLayout_8->addWidget(PlotCurve, 0, 0, 1, 1);


        gridLayout_6->addWidget(groupBox_2, 0, 2, 1, 1);

        gridLayout_6->setColumnStretch(0, 1);
        gridLayout_6->setColumnStretch(1, 1);
        gridLayout_6->setColumnStretch(2, 5);

        gridLayout_7->addLayout(gridLayout_6, 0, 0, 1, 1);

        MainWindow->setCentralWidget(centralWidget);
        menuBar = new QMenuBar(MainWindow);
        menuBar->setObjectName(QString::fromUtf8("menuBar"));
        menuBar->setGeometry(QRect(0, 0, 1579, 21));
        MainWindow->setMenuBar(menuBar);
        mainToolBar = new QToolBar(MainWindow);
        mainToolBar->setObjectName(QString::fromUtf8("mainToolBar"));
        MainWindow->addToolBar(Qt::TopToolBarArea, mainToolBar);
        statusBar = new QStatusBar(MainWindow);
        statusBar->setObjectName(QString::fromUtf8("statusBar"));
        MainWindow->setStatusBar(statusBar);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "\351\222\273\345\205\267\346\265\213\346\270\251\347\263\273\347\273\237", nullptr));
        groupBox->setTitle(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243\351\205\215\347\275\256", nullptr));
        label->setText(QCoreApplication::translate("MainWindow", "\347\253\257\345\217\243\345\220\215\347\247\260", nullptr));
        label_2->setText(QCoreApplication::translate("MainWindow", "\346\263\242\347\211\271\347\216\207", nullptr));
        label_3->setText(QCoreApplication::translate("MainWindow", "\346\225\260\346\215\256\344\275\215", nullptr));
        label_4->setText(QCoreApplication::translate("MainWindow", "\345\245\207\345\201\266", nullptr));
        label_5->setText(QCoreApplication::translate("MainWindow", "\345\201\234\346\255\242\344\275\215", nullptr));
        label_6->setText(QCoreApplication::translate("MainWindow", "\346\265\201\346\216\247\344\275\215", nullptr));
        label_8->setText(QCoreApplication::translate("MainWindow", "\344\273\216\347\253\231\345\234\260\345\235\200", nullptr));
        label_startReg->setText(QCoreApplication::translate("MainWindow", "\350\265\267\345\247\213\345\257\204\345\255\230\345\231\250", nullptr));
        startRegister->setText(QCoreApplication::translate("MainWindow", "0", nullptr));
        openButton->setText(QCoreApplication::translate("MainWindow", "\346\211\223\345\274\200\344\270\262\345\217\243", nullptr));
        saveButton->setText(QCoreApplication::translate("MainWindow", "\344\277\235\345\255\230\346\225\260\346\215\256", nullptr));
        mReadBtn->setText(QCoreApplication::translate("MainWindow", "\346\211\213\345\212\250\350\257\273\345\217\226\345\275\223\345\211\215\346\270\251\345\272\246", nullptr));
        groupBox_3->setTitle(QCoreApplication::translate("MainWindow", "\350\257\273\345\217\226\350\256\276\347\275\256", nullptr));
        btn_resettime->setText(QCoreApplication::translate("MainWindow", "\351\207\215\347\275\256\351\207\207\351\233\206", nullptr));
        checkBox_2->setText(QCoreApplication::translate("MainWindow", "\345\256\232\346\227\266", nullptr));
        label_10->setText(QCoreApplication::translate("MainWindow", "ms/\346\254\241", nullptr));
        label_7->setText(QCoreApplication::translate("MainWindow", "\351\207\207\346\240\267\346\200\273\346\227\266\351\225\277", nullptr));
        label_9->setText(QCoreApplication::translate("MainWindow", "\346\270\251\345\272\2461", nullptr));
        cb_tmp1->setText(QString());
        label_11->setText(QCoreApplication::translate("MainWindow", "\346\270\251\345\272\2462", nullptr));
        cb_tmp2->setText(QString());
        nowwd_2->setText(QString());
        label_12->setText(QCoreApplication::translate("MainWindow", "\346\270\251\345\272\2463", nullptr));
        cb_tmp3->setText(QString());
        nowwd_3->setText(QString());
        label_13->setText(QCoreApplication::translate("MainWindow", "\346\270\251\345\272\2464", nullptr));
        cb_tmp4->setText(QString());
        nowwd_4->setText(QString());
        label_14->setText(QCoreApplication::translate("MainWindow", "\346\270\251\345\272\2465", nullptr));
        cb_tmp5->setText(QString());
        nowwd_5->setText(QString());
        label_15->setText(QCoreApplication::translate("MainWindow", "\346\270\251\345\272\2466", nullptr));
        cb_tmp6->setText(QString());
        nowwd_6->setText(QString());
        label_16->setText(QCoreApplication::translate("MainWindow", "\346\270\251\345\272\2467", nullptr));
        cb_tmp7->setText(QString());
        nowwd_7->setText(QString());
        label_17->setText(QCoreApplication::translate("MainWindow", "\346\270\251\345\272\2468", nullptr));
        cb_tmp8->setText(QString());
        nowwd_8->setText(QString());
        label_18->setText(QCoreApplication::translate("MainWindow", "\346\270\251\345\272\2469", nullptr));
        cb_tmp9->setText(QString());
        nowwd_9->setText(QString());
        label_19->setText(QCoreApplication::translate("MainWindow", "\346\270\251\345\272\24610", nullptr));
        cb_tmp10->setText(QString());
        nowwd_10->setText(QString());
        groupBox_2->setTitle(QCoreApplication::translate("MainWindow", " \345\256\236\346\227\266\346\233\262\347\272\277", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
