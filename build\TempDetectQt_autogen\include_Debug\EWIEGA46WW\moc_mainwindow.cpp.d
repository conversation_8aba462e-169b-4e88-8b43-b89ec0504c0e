C:/Users/<USER>/Desktop/ThermoLogger/build/TempDetectQt_autogen/include_Debug/EWIEGA46WW/moc_mainwindow.cpp: C:/Users/<USER>/Desktop/ThermoLogger/mainwindow.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QCache \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QDateTime \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QDeadlineTimer \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QDebug \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QEvent \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QFlags \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QList \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QMargins \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QMetaType \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QMultiMap \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QObject \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QPair \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QPointF \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QPointer \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QRect \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QScopedPointer \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QSharedDataPointer \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QSharedPointer \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QSize \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QSizeF \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QStack \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QString \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QStringList \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QTimer \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QVariant \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QVector \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QtCore \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QtCoreDepends \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QtNumeric \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qabstractanimation.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qabstracteventdispatcher.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qabstractitemmodel.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qabstractnativeeventfilter.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qabstractproxymodel.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qabstractstate.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qabstracttransition.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qalgorithms.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qanimationgroup.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qarraydata.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qarraydataops.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qarraydatapointer.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qatomic.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qbasicatomic.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qbasictimer.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qbitarray.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qbuffer.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qbytearray.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qbytearraylist.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qbytearraymatcher.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcache.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcalendar.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcborarray.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcborcommon.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcbormap.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcborstream.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcborstreamreader.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcborstreamwriter.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcborvalue.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qchar.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcollator.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcommandlineoption.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcommandlineparser.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcompilerdetection.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qconcatenatetablesproxymodel.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qconfig.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcontainerfwd.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcontainertools_impl.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcontiguouscache.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcoreapplication.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcoreevent.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcryptographichash.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qdatastream.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qdatetime.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qdeadlinetimer.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qdebug.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qdir.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qdiriterator.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qeasingcurve.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qelapsedtimer.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qendian.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qeventloop.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qeventtransition.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qexception.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qfactoryinterface.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qfile.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qfiledevice.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qfileinfo.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qfileselector.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qfilesystemwatcher.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qfinalstate.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qflags.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qfloat16.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qfuture.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qfutureinterface.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qfuturesynchronizer.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qfuturewatcher.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qgenericatomic.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qglobal.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qglobalstatic.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qhash.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qhashfunctions.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qhistorystate.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qidentityproxymodel.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qiodevice.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qisenum.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qitemselectionmodel.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qiterator.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qjsonarray.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qjsondocument.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qjsonobject.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qjsonvalue.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qlibrary.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qlibraryinfo.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qline.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qlinkedlist.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qlist.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qlocale.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qlockfile.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qlogging.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qloggingcategory.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qmap.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qmargins.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qmath.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qmessageauthenticationcode.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qmetaobject.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qmetatype.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qmimedata.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qmimedatabase.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qmimetype.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qmutex.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qnamespace.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qnumeric.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qobject.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qobject_impl.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qobjectcleanuphandler.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qobjectdefs.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qobjectdefs_impl.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qoperatingsystemversion.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qpair.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qparallelanimationgroup.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qpauseanimation.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qplugin.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qpluginloader.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qpoint.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qpointer.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qprocess.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qprocessordetection.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qpropertyanimation.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qqueue.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qrandom.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qreadwritelock.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qrect.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qrefcount.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qregexp.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qregularexpression.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qresource.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qresultstore.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qrunnable.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsavefile.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qscopedpointer.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qscopedvaluerollback.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qscopeguard.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsemaphore.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsequentialanimationgroup.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qset.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsettings.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qshareddata.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsharedmemory.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsharedpointer.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsharedpointer_impl.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsignalmapper.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsignaltransition.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsize.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsocketnotifier.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsortfilterproxymodel.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstack.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstandardpaths.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstate.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstatemachine.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstorageinfo.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstring.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstringalgorithms.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstringbuilder.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstringlist.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstringlistmodel.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstringliteral.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstringmatcher.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstringview.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsysinfo.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsystemdetection.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsystemsemaphore.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qt_windows.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtcore-config.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtcoreversion.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtemporarydir.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtemporaryfile.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtextboundaryfinder.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtextcodec.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtextstream.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qthread.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qthreadpool.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qthreadstorage.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtimeline.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtimer.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtimezone.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtranslator.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtransposeproxymodel.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtypeinfo.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtypetraits.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qurl.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qurlquery.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/quuid.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qvariant.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qvariantanimation.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qvarlengtharray.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qvector.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qversionnumber.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qversiontagging.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qwaitcondition.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qwineventnotifier.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qxmlstream.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QImage \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QMatrix3x3 \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QMatrix4x4 \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QMouseEvent \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QOpenGLContext \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QPaintDevice \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QPaintDeviceWindow \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QPaintEvent \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QPainter \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QPixmap \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QSurfaceFormat \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QTransform \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QVector3D \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QWheelEvent \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QWindow \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QtGui \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/QtGuiDepends \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qabstracttextdocumentlayout.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qaccessible.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qaccessiblebridge.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qaccessibleobject.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qaccessibleplugin.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qbackingstore.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qbitmap.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qbrush.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qclipboard.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qcolor.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qcolorspace.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qcolortransform.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qcursor.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qdesktopservices.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qdrag.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qevent.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qfont.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qfontdatabase.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qfontinfo.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qfontmetrics.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qgenericmatrix.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qgenericplugin.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qgenericpluginfactory.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qglyphrun.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qguiapplication.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qicon.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qiconengine.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qiconengineplugin.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qimage.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qimageiohandler.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qimagereader.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qimagewriter.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qinputmethod.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qkeysequence.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qmatrix.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qmatrix4x4.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qmovie.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qoffscreensurface.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopengl.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopenglbuffer.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopenglcontext.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopengldebug.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopenglext.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopenglextrafunctions.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopenglframebufferobject.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopenglfunctions.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopenglpaintdevice.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopenglpixeltransferoptions.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopenglshaderprogram.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopengltexture.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopengltextureblitter.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopengltimerquery.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopenglversionfunctions.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopenglvertexarrayobject.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qopenglwindow.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpagedpaintdevice.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpagelayout.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpagesize.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpaintdevice.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpaintdevicewindow.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpaintengine.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpainter.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpainterpath.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpalette.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpdfwriter.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpen.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpicture.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpictureformatplugin.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpixelformat.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpixmap.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpixmapcache.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qpolygon.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qquaternion.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qrasterwindow.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qrawfont.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qregion.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qrgb.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qrgba64.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qscreen.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qsessionmanager.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qstandarditemmodel.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qstatictext.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qstylehints.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qsurface.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qsurfaceformat.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qsyntaxhighlighter.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtextcursor.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtextdocument.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtextdocumentfragment.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtextdocumentwriter.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtextformat.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtextlayout.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtextlist.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtextobject.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtextoption.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtexttable.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtgui-config.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtguiglobal.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtguiversion.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtouchdevice.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qtransform.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qvalidator.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qvector2d.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qvector3d.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qvector4d.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qwindow.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qwindowdefs.h \
  C:/Qt/5.15.0/mingw81_64/include/QtGui/qwindowdefs_win.h \
  C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport/QtPrintSupport \
  C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport/QtPrintSupportDepends \
  C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport/qabstractprintdialog.h \
  C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport/qpagesetupdialog.h \
  C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport/qprintdialog.h \
  C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport/qprintengine.h \
  C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport/qprinter.h \
  C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport/qprinterinfo.h \
  C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport/qprintpreviewdialog.h \
  C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport/qprintpreviewwidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport/qtprintsupport-config.h \
  C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport/qtprintsupportglobal.h \
  C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport/qtprintsupportversion.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/QModbusRtuSerialMaster \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qmodbusclient.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qmodbusdataunit.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qmodbusdevice.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qmodbuspdu.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qmodbusreply.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qmodbusrtuserialmaster.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qtserialbus-config.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qtserialbusglobal.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialPort/QSerialPort \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialPort/qserialport.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialPort/qserialportglobal.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/QCommonStyle \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/QLayout \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/QMainWindow \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/QScrollerProperties \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/QWidget \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/QtWidgets \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/QtWidgetsDepends \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qabstractbutton.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qabstractitemdelegate.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qabstractitemview.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qabstractscrollarea.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qabstractslider.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qabstractspinbox.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qaccessiblewidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qaction.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qactiongroup.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qapplication.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qboxlayout.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qbuttongroup.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qcalendarwidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qcheckbox.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qcolordialog.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qcolormap.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qcolumnview.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qcombobox.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qcommandlinkbutton.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qcommonstyle.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qcompleter.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qdatawidgetmapper.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qdatetimeedit.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qdesktopwidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qdial.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qdialog.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qdialogbuttonbox.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qdirmodel.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qdockwidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qdrawutil.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qerrormessage.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qfiledialog.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qfileiconprovider.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qfilesystemmodel.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qfocusframe.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qfontcombobox.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qfontdialog.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qformlayout.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qframe.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgesture.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgesturerecognizer.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicsanchorlayout.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicseffect.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicsgridlayout.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicsitem.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicsitemanimation.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicslayout.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicslayoutitem.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicslinearlayout.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicsproxywidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicsscene.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicssceneevent.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicstransform.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicsview.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgraphicswidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgridlayout.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qgroupbox.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qheaderview.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qinputdialog.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qitemdelegate.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qitemeditorfactory.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qkeyeventtransition.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qkeysequenceedit.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qlabel.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qlayout.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qlayoutitem.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qlcdnumber.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qlineedit.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qlistview.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qlistwidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qmainwindow.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qmdiarea.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qmdisubwindow.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qmenu.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qmenubar.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qmessagebox.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qmouseeventtransition.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qopenglwidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qplaintextedit.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qprogressbar.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qprogressdialog.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qproxystyle.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qpushbutton.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qradiobutton.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qrubberband.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qscrollarea.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qscrollbar.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qscroller.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qscrollerproperties.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qshortcut.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qsizegrip.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qsizepolicy.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qslider.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qspinbox.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qsplashscreen.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qsplitter.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qstackedlayout.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qstackedwidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qstatusbar.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qstyle.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qstyleditemdelegate.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qstylefactory.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qstyleoption.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qstylepainter.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qstyleplugin.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qsystemtrayicon.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtabbar.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtableview.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtablewidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtabwidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtextbrowser.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtextedit.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtoolbar.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtoolbox.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtoolbutton.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtooltip.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtreeview.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtreewidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtreewidgetitemiterator.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtwidgets-config.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qtwidgetsversion.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qundogroup.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qundostack.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qundoview.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qwhatsthis.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qwidget.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qwidgetaction.h \
  C:/Qt/5.15.0/mingw81_64/include/QtWidgets/qwizard.h \
  C:/Users/<USER>/Desktop/ThermoLogger/qcustomplot.h
