﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\ThermoLogger\build\TempDetectQt_autogen\mocs_compilation_Debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\ThermoLogger\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\ThermoLogger\mainwindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\ThermoLogger\modbusmanager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\ThermoLogger\qcustomplot.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\ThermoLogger\temperaturedata.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\ThermoLogger\configurationmanager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\ThermoLogger\build\TempDetectQt_autogen\mocs_compilation_Release.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\ThermoLogger\build\TempDetectQt_autogen\mocs_compilation_MinSizeRel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\ThermoLogger\build\TempDetectQt_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\Desktop\ThermoLogger\mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\ThermoLogger\qcustomplot.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\ThermoLogger\modbusmanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\ThermoLogger\temperaturedata.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\ThermoLogger\configurationmanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\ThermoLogger\build\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\ThermoLogger\mainwindow.ui" />
    <CustomBuild Include="C:\Users\<USER>\Desktop\ThermoLogger\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{B0F7B5F6-2082-3321-9A89-95463900A412}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{83D028BD-61B0-3C2F-AE05-F2E838454E41}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
