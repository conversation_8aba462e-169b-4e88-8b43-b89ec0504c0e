C:/Users/<USER>/Desktop/ThermoLogger/build/TempDetectQt_autogen/include_Debug/EWIEGA46WW/moc_modbusmanager.cpp: C:/Users/<USER>/Desktop/ThermoLogger/modbusmanager.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/QObject \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qalgorithms.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qarraydata.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qatomic.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qbasicatomic.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qbytearray.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qbytearraylist.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qchar.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcompilerdetection.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qconfig.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcontainerfwd.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qcontainertools_impl.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qdatastream.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qflags.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qglobal.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qglobalstatic.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qhashfunctions.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qiodevice.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qiterator.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qlist.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qlogging.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qmap.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qmetatype.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qmutex.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qnamespace.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qnumeric.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qobject.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qobject_impl.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qobjectdefs.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qobjectdefs_impl.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qpair.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qprocessordetection.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qrefcount.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qregexp.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qscopedpointer.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstring.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstringalgorithms.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstringlist.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstringliteral.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstringmatcher.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qstringview.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsysinfo.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qsystemdetection.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtcore-config.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qtypeinfo.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qvarlengtharray.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qvector.h \
  C:/Qt/5.15.0/mingw81_64/include/QtCore/qversiontagging.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/QModbusRtuSerialMaster \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qmodbusclient.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qmodbusdataunit.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qmodbusdevice.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qmodbuspdu.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qmodbusreply.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qmodbusrtuserialmaster.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qtserialbus-config.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialBus/qtserialbusglobal.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialPort/QSerialPort \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialPort/qserialport.h \
  C:/Qt/5.15.0/mingw81_64/include/QtSerialPort/qserialportglobal.h
