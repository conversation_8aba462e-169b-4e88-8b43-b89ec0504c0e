^C:\USERS\<USER>\DESKTOP\THERMOLOGGER\MAINWINDOW.UI
setlocal
C:\Qt\5.15.0\mingw81_64\bin\uic.exe -o C:/Users/<USER>/Desktop/ThermoLogger/build/ui_mainwindow.h C:/Users/<USER>/Desktop/ThermoLogger/mainwindow.ui
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DESKTOP\THERMOLOGGER\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/ThermoLogger -BC:/Users/<USER>/Desktop/ThermoLogger/build --check-stamp-file C:/Users/<USER>/Desktop/ThermoLogger/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
